<script lang="ts">
	import csFlag from '$lib/assets/img/flags/cs.webp';
	// import uaFlag from '$lib/assets/img/flags/ua.webp';
	import { Flex } from '$lib/components';
	import PopoverWrapper from '$lib/components/shared/primitives/Popover/PopoverWrapper.svelte';
	import { loadTranslations, locale, locales } from '$lib/translations/config';

	const languages = [
		{ code: 'cs', name: '<PERSON><PERSON><PERSON><PERSON>', flag: csFlag }
		// { code: 'ua', name: 'Українська', flag: uaFlag }
	];

	const handleLanguageChange = async (language: string) => {
		await loadTranslations(language);
	};

	const getLanguageName = (code: string) => {
		const language = languages.find((l) => l.code === code);
		return language ? language.name : code.toUpperCase();
	};

	const getLanguageFlag = (code: string) => {
		const language = languages.find((l) => l.code === code);
		return language ? language.flag : '';
	};
</script>

<PopoverWrapper>
	{#snippet target()}
		<img class="flag" src={getLanguageFlag($locale)} alt="Flag of {getLanguageName($locale)}" />
	{/snippet}

	{#snippet content()}
		<Flex direction="col" as="ul" gap="l">
			{#each $locales.filter((l) => languages.find((lang) => lang.code === l)) as l (l)}
				<li>
					<button on:click={() => handleLanguageChange(l)}>
						<img
							class="flag"
							src={getLanguageFlag(l)}
							alt="Flag of {getLanguageName(l)}"
						/>
						<span class="name">{getLanguageName(l)}</span>
					</button>
				</li>
			{/each}
		</Flex>
	{/snippet}
</PopoverWrapper>

<style lang="scss">
	@use '$lib/assets/styles/hoverable' as hoverable;

	.flag {
		width: 1.25rem;
		height: 1.25rem;
		border-radius: 50%;
	}

	button {
		border: none;
		background: none;
		cursor: pointer;
		display: flex;
		width: 100%;
		align-items: center;
		gap: var(--spacing-s);
		font-size: var(--font-size-text);
		color: var(--color-dark);
		transition: color 0.2s ease;

		@include hoverable.hoverable();

		&:hover {
			color: var(--color-primary);
		}
	}
</style>
