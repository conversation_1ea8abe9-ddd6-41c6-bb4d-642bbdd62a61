import type { Cell, ColumnDef, Header, RowData } from '@tanstack/table-core';
import type { Component } from 'svelte';

type TableRowData = RowData;
type CellValue = unknown;

export interface HeaderCellProps {
	header: Header<TableRowData, CellValue>;
}

export interface DataCellProps {
	cell: Cell<TableRowData, CellValue>;
}

export interface DataTableProps<TData extends TableRowData = TableRowData> {
	columns: DataTableColumnDefinitions<TData>[];
	data: TData[];
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type HeaderCellRenderer = Component<HeaderCellProps & any, any, any>;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type DataCellRenderer = Component<DataCellProps & any, any, any>;

declare module '@tanstack/table-core' {
	interface ColumnMeta<TData extends RowData, TValue> {
		headerCellRenderer?: HeaderCellRenderer;
		dataCellRenderer?: DataCellRenderer;
		dataCellProps?: Record<string, unknown>;
	}
}

export type DataTableColumnDefinitions<TData extends TableRowData = TableRowData> = ColumnDef<
	TData,
	CellValue
>;

export function getPaginationStructure(
	current: number,
	total: number,
	delta = 2
): (number | string)[] {
	const pages: Set<number> = new Set([1, total]);

	for (let i = current - delta; i <= current + delta; i++) {
		if (i >= 1 && i <= total) {
			pages.add(i);
		}
	}

	const sortedPages: number[] = Array.from(pages).sort((a, b) => a - b);

	const result: (number | string)[] = [];
	let lastPage: number | null = null;

	for (const page of sortedPages) {
		if (lastPage !== null) {
			if (page - lastPage === 2) {
				result.push(lastPage + 1);
			} else if (page - lastPage > 2) {
				result.push('...');
			}
		}
		result.push(page);
		lastPage = page;
	}

	return result;
}
