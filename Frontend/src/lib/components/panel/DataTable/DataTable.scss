.data-table-container {
	width: 100%;
	flex: 1;
	display: flex;
	flex-direction: column;
	background-color: var(--color-light);
	border-radius: var(--border-radius-l);
	border: 1px solid var(--color-border-light);
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	overflow: hidden;

	.table-scroll-wrapper {
		overflow-x: auto;
		flex: 1;
	}
}

.data-table {
	width: 100%;
	border-collapse: collapse;
	border-bottom: 1px solid var(--color-border-light);

	thead {
		border-bottom: 1px solid var(--color-border-light);

		th {
			padding: var(--spacing-m) var(--spacing-ml);
			text-align: left;
			font-weight: bold;
			font-size: var(--font-size-text);
			border-right: 1px solid var(--color-border-light);
			position: relative;
			vertical-align: center;

			&:last-child {
				border-right: none;
			}
		}
	}

	tbody {
		.empty-message {
			color: var(--color-muted);
			font-style: italic;
		}

		tr {
			border-bottom: 1px solid var(--color-border-light);
			transition: background-color 0.2s ease;

			&:hover {
				background-color: var(--color-section);
			}

			&:last-child {
				border-bottom: none;
			}

			&.selected {
				background-color: rgba(66, 126, 244, 0.1);

				&:hover {
					background-color: rgba(66, 126, 244, 0.15);
				}
			}
		}

		td {
			padding: var(--spacing-m) var(--spacing-ml);
			font-size: var(--font-size-text);
			color: var(--color-dark);
			border-right: 1px solid var(--color-border-light);

			&:last-child {
				border-right: none;
			}
		}
	}
}

.table-pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: auto;
	padding: var(--spacing-m) var(--spacing-ml);
	border-top: 1px solid var(--color-border-light);
	background-color: var(--color-light);

	.pagination-btn {
		width: 2rem;
		height: 2rem;
		border: none;
		background: none;
		cursor: pointer;
		border-radius: var(--border-radius-s);
		transition: background-color 0.2s ease;

		&:not(:disabled):not(.active):hover {
			background-color: var(--color-section);
		}

		&.active {
			font-weight: bold;
			background-color: var(--color-primary);
			color: var(--color-light);
		}
	}
}

@media (max-width: 768px) {
	.data-table {
		thead {
			th {
				padding: var(--spacing-sm) var(--spacing-m);
				font-size: var(--font-size-small);
			}
		}

		tbody {
			td {
				padding: var(--spacing-sm) var(--spacing-m);
				font-size: var(--font-size-small);

				&.truncate {
					max-width: 120px;
				}
			}
		}
	}

	.table-pagination {
		flex-direction: column;
		gap: var(--spacing-m);
		text-align: center;

		.pagination-controls {
			justify-content: center;
		}
	}
}
