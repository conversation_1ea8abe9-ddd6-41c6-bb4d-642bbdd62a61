<script lang="ts">
	import { Mail } from '@lucide/svelte';

	import type { DataCellProps } from '../../DataTable.ts';

	let { cell }: DataCellProps = $props();
	const email = $derived(String(cell.getValue() ?? '')) as string;
</script>

<a class="email-cell" href={'mailto:' + email} title={email}>
	<Mail size={14} />
	<span>{email}</span>
</a>

<style lang="scss">
	.email-cell {
		display: inline-flex;
		align-items: center;
		gap: var(--spacing-xs);
		color: var(--color-primary);
		text-decoration: none;

		&:hover {
			text-decoration: underline;
		}
	}
</style>
