<script lang="ts">
	import { Image, ImageOff } from '@lucide/svelte';

	import type { DataCellProps } from '../../DataTable.ts';

	let { cell }: DataCellProps = $props();

	const imageUrl = $derived(String(cell.getValue() ?? ''));

	let imageLoaded = $state(false);
	let imageError = $state(false);

	function handleImageLoad() {
		imageLoaded = true;
		imageError = false;
	}

	function handleImageError() {
		imageError = true;
		imageLoaded = false;
	}

	function handleImageClick() {
		if (imageUrl && !imageError) {
			window.open(imageUrl, '_blank');
		}
	}
</script>

<div class="image-cell">
	{#if imageUrl && !imageError}
		<div
			class="image-container"
			role="button"
			tabindex="0"
			onclick={handleImageClick}
			onkeydown={(e) => e.key === 'Enter' && handleImageClick()}
		>
			<img
				src={imageUrl}
				alt=""
				class="thumbnail"
				class:loaded={imageLoaded}
				onload={handleImageLoad}
				onerror={handleImageError}
			/>
			{#if !imageLoaded && !imageError}
				<div class="loading-placeholder">
					<Image size={16} />
				</div>
			{/if}
		</div>
	{:else}
		<div class="no-image">
			<ImageOff size={16} />
			<span>No image</span>
		</div>
	{/if}
</div>

<style lang="scss">
	.image-cell {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: var(--spacing-xs);
	}

	.image-container {
		position: relative;
		width: 40px;
		height: 40px;
		border-radius: var(--border-radius-s);
		overflow: hidden;
		cursor: pointer;
		border: 1px solid var(--color-border-light);
		transition: all 0.2s ease;

		&:hover {
			transform: scale(1.1);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		}

		&:focus {
			outline: 2px solid var(--color-primary);
			outline-offset: 2px;
		}
	}

	.thumbnail {
		width: 100%;
		height: 100%;
		object-fit: cover;
		opacity: 0;
		transition: opacity 0.2s ease;

		&.loaded {
			opacity: 1;
		}
	}

	.loading-placeholder {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: var(--color-section);
		color: var(--color-muted);
	}

	.no-image {
		display: flex;
		align-items: center;
		gap: var(--spacing-xs);
		color: var(--color-muted);
		font-size: var(--font-size-small);
		padding: var(--spacing-xs);
	}
</style>
