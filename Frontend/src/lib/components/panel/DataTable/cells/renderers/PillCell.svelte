<script lang="ts">
	import type { DataCellProps } from '../../DataTable.ts';

	interface PillCellProps extends DataCellProps {
		hexColor: string;
	}

	let { cell, hexColor }: PillCellProps = $props();
	const value = $derived(String(cell.getValue() ?? '')) as string;
</script>

<span class="status-pill" style={`--pill-bg:${hexColor}30; --pill-fg:${hexColor}`}>
	{value}
</span>

<style lang="scss">
	.status-pill {
		display: inline-block;
		padding: 0.1rem 0.5rem;
		border-radius: 10em;
		font-size: 0.85em;
		font-weight: 600;
		text-transform: capitalize;
		background-color: var(--pill-bg);
		color: var(--pill-fg);
	}
</style>
