<script lang="ts">
	import { locale } from '$lib/translations/config';

	import type { DataCellProps } from '../../DataTable.ts';

	let { cell }: DataCellProps = $props();

	const value = $derived(cell.getValue() as Date | null);
	const text = $derived.by(() => {
		if (!value) return 'Never';
		return value.toLocaleDateString($locale, {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit'
		});
	});
</script>

<span class="date-cell" title={value ? value.toISOString() : 'Never'}>
	{text}
</span>

<style lang="scss">
	.date-cell {
		white-space: nowrap;
	}
</style>
