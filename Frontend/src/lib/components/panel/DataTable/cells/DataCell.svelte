<script lang="ts">
	import { exists } from '$lib/appmaxx/util/index.js';
	import { Text } from '$lib/components';

	import type { DataCellProps } from '../DataTable.ts';

	let { cell }: DataCellProps = $props();

	const Renderer = $derived(cell.column.columnDef.meta?.dataCellRenderer);
	const rendererProps = $derived(cell.column.columnDef.meta?.dataCellProps ?? {});

	// Fallback rendering when no custom renderer is provided
	const cellContent = $derived.by(() => {
		const cellDef = cell.column.columnDef.cell;
		if (typeof cellDef === 'string') {
			return cellDef;
		}
		if (typeof cellDef === 'function') {
			return cellDef(cell.getContext());
		}
		return cell.getValue();
	});
</script>

<td class="data-cell">
	{#if exists(Renderer)}
		<Renderer {cell} {...rendererProps} />
	{:else}
		<Text wrap="nowrap">
			{cellContent}
		</Text>
	{/if}
</td>

<style lang="scss">
	.data-cell {
		white-space: nowrap;
	}
</style>
