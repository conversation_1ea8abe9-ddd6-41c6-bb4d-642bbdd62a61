{"dataGrid": {"id": "ID", "firstName": "Jméno", "lastName": "Příjmení", "email": "Email", "emailStudent": "Studentský email", "role": "Role", "language": "Jazyk", "emailVerified": "<PERSON><PERSON>", "createdAt": "Vytvořeno", "phone": "Telefon", "description": "<PERSON><PERSON>", "wageTax": "Daň ze mzdy", "status": "Stav", "billingFirstName": "Fakturační jméno", "billingLastName": "Fakturační příjmení", "billingAddress": "Fakturační adresa", "billingCity": "Fakturačn<PERSON> město", "billingZip": "Fakturační PSČ", "billingPhone": "Fakturační telefon", "billingEmail": "Fakturační email"}, "status": {"active": "Aktivní", "archived": "Archivováno"}, "emailVerified": {"yes": "<PERSON><PERSON>", "no": "Ne"}, "common": {"notAvailable": "-"}, "errors": {"loadingStudents": "Chyba při načítání studentů", "loadingTeachers": "Chyba při načítání le<PERSON>ů", "loadingManagers": "Chyba při načítání man<PERSON>ů", "loadingAdmins": "Chyba při načítání administrátorů"}, "students": {"title": "Studenti"}, "teachers": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "managers": {"title": "Man<PERSON>ž<PERSON><PERSON><PERSON>"}, "admins": {"title": "Admini<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}