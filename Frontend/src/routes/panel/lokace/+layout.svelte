<script lang="ts">
	import { Button, Flex, Heading, NavTab, PageTransition, Spacer } from '$lib/components';
	import HorizontalTabNav from '$lib/components/panel/HorizontalTabNav/HorizontalTabNav.svelte';

	let { children } = $props();
</script>

<Flex direction="row" align="center">
	<Heading as="h1" size="2">Lokace</Heading>
	<Spacer grow="1" />

	<HorizontalTabNav>
		<NavTab href="/panel/lokace" label="Seznam lokací" />
		<NavTab href="/panel/lokace/mapa" label="Mapa lokací" />
	</HorizontalTabNav>

	<Spacer direction="horizontal" size="m" />
	<Button disabled variant="success" size="medium">Přidat lokaci</Button>
</Flex>

<Spacer direction="vertical" size="l" />

<PageTransition key="panel.locations">
	{@render children()}
</PageTransition>
