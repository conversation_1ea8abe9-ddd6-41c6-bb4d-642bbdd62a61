<script lang="ts">
	import { DataTable, Loader, Text } from '$lib/components';
	import EmailCell from '$lib/components/panel/DataTable/cells/renderers/EmailCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import type { DataTableColumnDefinitions } from '$lib/components/panel/DataTable/DataTable';
	import UsersPageHeading from '$lib/components/panel/users/UsersPageHeading.svelte';
	import type { Student } from '$lib/services';
	import { userService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	const studentsQuery = userService.listStudents();

	let columns = $state<DataTableColumnDefinitions<Student>[]>([
		{
			accessorKey: 'firstName',
			header: $t('users.dataGrid.firstName')
		},
		{
			accessorKey: 'lastName',
			header: $t('users.dataGrid.lastName')
		},
		{
			accessorKey: 'email',
			header: $t('users.dataGrid.email'),
			meta: { dataCellRenderer: EmailCell }
		},
		{
			accessorKey: 'emailStudent',
			header: $t('users.dataGrid.emailStudent'),
			meta: { dataCellRenderer: EmailCell }
		},
		{
			accessorKey: 'role',
			header: $t('users.dataGrid.role')
		},
		{
			accessorKey: 'lang',
			header: $t('users.dataGrid.language')
		},
		{
			accessorKey: 'emailVerifiedAt',
			header: $t('users.dataGrid.emailVerified'),
			cell: ({ getValue }) => {
				const value = getValue() as Date | null;
				return value ? $t('users.emailVerified.yes') : $t('users.emailVerified.no');
			}
		},
		{
			accessorKey: 'createdAt',
			header: $t('users.dataGrid.createdAt'),
			cell: ({ getValue }) => {
				const value = getValue() as string;
				return new Date(value).toLocaleDateString();
			}
		},
		{
			accessorKey: 'billingFirstName',
			header: $t('users.dataGrid.billingFirstName'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'billingLastName',
			header: $t('users.dataGrid.billingLastName'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'billingAddress',
			header: $t('users.dataGrid.billingAddress'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'billingCity',
			header: $t('users.dataGrid.billingCity'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'billingZip',
			header: $t('users.dataGrid.billingZip'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'billingPhone',
			header: $t('users.dataGrid.billingPhone'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'billingEmail',
			header: $t('users.dataGrid.billingEmail'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'archivedAt',
			header: $t('users.dataGrid.status'),
			cell: ({ getValue }) => {
				const value = getValue();
				return value ? $t('users.status.archived') : $t('users.status.active');
			},
			meta: { dataCellRenderer: PillCell }
		}
	]);
</script>

<UsersPageHeading title={$t('users.students.title')} queryKey={['users', 'students']} />

{#if $studentsQuery.isPending}
	<Loader fillPage />
{:else if $studentsQuery.error}
	<Text><T key="users.errors.loadingStudents" />: {$studentsQuery.error.message}</Text>
{:else if $studentsQuery.data}
	<DataTable data={$studentsQuery.data} {columns} />
{/if}
