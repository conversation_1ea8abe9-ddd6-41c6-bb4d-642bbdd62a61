<script lang="ts">
	import { DataTable, Loader, Text } from '$lib/components';
	import EmailCell from '$lib/components/panel/DataTable/cells/renderers/EmailCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import type { DataTableColumnDefinitions } from '$lib/components/panel/DataTable/DataTable';
	import UsersPageHeading from '$lib/components/panel/users/UsersPageHeading.svelte';
	import type { Manager } from '$lib/services';
	import { userService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	const managersQuery = userService.listManagers();

	let columns = $state<DataTableColumnDefinitions<Manager>[]>([
		{
			accessorKey: 'firstName',
			header: $t('users.dataGrid.firstName')
		},
		{
			accessorKey: 'lastName',
			header: $t('users.dataGrid.lastName')
		},
		{
			accessorKey: 'email',
			header: $t('users.dataGrid.email'),
			meta: { dataCellRenderer: EmailCell }
		},
		{
			accessorKey: 'role',
			header: $t('users.dataGrid.role')
		},
		{
			accessorKey: 'lang',
			header: $t('users.dataGrid.language')
		},
		{
			accessorKey: 'emailVerifiedAt',
			header: $t('users.dataGrid.emailVerified'),
			cell: ({ getValue }) => {
				const value = getValue() as Date | null;
				return value ? $t('users.emailVerified.yes') : $t('users.emailVerified.no');
			}
		},
		{
			accessorKey: 'createdAt',
			header: $t('users.dataGrid.createdAt'),
			cell: ({ getValue }) => {
				const value = getValue() as string;
				return new Date(value).toLocaleDateString();
			}
		},
		{
			accessorKey: 'archivedAt',
			header: $t('users.dataGrid.status'),
			cell: ({ getValue }) => {
				const value = getValue() as Date | null;
				return value ? $t('users.status.archived') : $t('users.status.active');
			},
			meta: { dataCellRenderer: PillCell }
		}
	]);
</script>

<UsersPageHeading title={$t('users.managers.title')} queryKey={['users', 'managers']} />

{#if $managersQuery.isPending}
	<Loader fillPage />
{:else if $managersQuery.error}
	<Text><T key="users.errors.loadingManagers" />: {$managersQuery.error.message}</Text>
{:else if $managersQuery.data}
	<DataTable data={$managersQuery.data} {columns} />
{/if}
