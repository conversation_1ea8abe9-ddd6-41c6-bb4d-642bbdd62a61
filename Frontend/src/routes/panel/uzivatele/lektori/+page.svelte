<script lang="ts">
	import type { DataTableColumnDefinitions } from '$lib/components';
	import { DataTable, Loader, Text } from '$lib/components';
	import EmailCell from '$lib/components/panel/DataTable/cells/renderers/EmailCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import UsersPageHeading from '$lib/components/panel/users/UsersPageHeading.svelte';
	import type { Teacher } from '$lib/services';
	import { userService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	const teachersQuery = userService.listTeachers();

	let columns = $state<DataTableColumnDefinitions<Teacher>[]>([
		{
			accessorKey: 'firstName',
			header: $t('users.dataGrid.firstName')
		},
		{
			accessorKey: 'lastName',
			header: $t('users.dataGrid.lastName')
		},
		{
			accessorKey: 'email',
			header: $t('users.dataGrid.email'),
			meta: { dataCellRenderer: EmailCell }
		},
		{
			accessorKey: 'role',
			header: $t('users.dataGrid.role')
		},
		{
			accessorKey: 'lang',
			header: $t('users.dataGrid.language')
		},
		{
			accessorKey: 'emailVerifiedAt',
			header: $t('users.dataGrid.emailVerified'),
			cell: ({ getValue }) => {
				const value = getValue() as Date | null;
				return value ? $t('users.emailVerified.yes') : $t('users.emailVerified.no');
			}
		},
		{
			accessorKey: 'createdAt',
			header: $t('users.dataGrid.createdAt'),
			cell: ({ getValue }) => {
				const value = getValue() as string;
				return new Date(value).toLocaleDateString();
			}
		},
		{
			accessorKey: 'phone',
			header: $t('users.dataGrid.phone'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'description',
			header: $t('users.dataGrid.description'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value
					? value.length > 50
						? `${value.substring(0, 50)}...`
						: value
					: $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'wageTax',
			header: $t('users.dataGrid.wageTax'),
			cell: ({ getValue }) => {
				const value = getValue() as number | null;
				return value !== null ? `${value}%` : $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'archivedAt',
			header: $t('users.dataGrid.status'),
			cell: ({ getValue }) => {
				const value = getValue() as Date | null;
				return value ? $t('users.status.archived') : $t('users.status.active');
			},
			meta: { dataCellRenderer: PillCell }
		}
	]);
</script>

<UsersPageHeading title={$t('users.teachers.title')} queryKey={['users', 'teachers']} />

{#if $teachersQuery.isPending}
	<Loader fillPage />
{:else if $teachersQuery.error}
	<Text><T key="users.errors.loadingTeachers" />: {$teachersQuery.error.message}</Text>
{:else if $teachersQuery.data}
	<DataTable data={$teachersQuery.data} {columns} />
{/if}
